import {ColumnMeta} from '@tanstack/react-table';
import React from 'react';

declare module '@tanstack/react-table' {
    interface ColumnMeta<TData, TValue> {
        notExport?: boolean;
        exportHTML?: (cell: Cell<TData, TValue>) => string;
        rowClassName?: (row: TData) => string | undefined;
        rowStyle?: (row: TData) => React.CSSProperties | undefined;
    }
}

declare global {
    interface Window {
        gtag: (...args: any[]) => void;
    }
}
