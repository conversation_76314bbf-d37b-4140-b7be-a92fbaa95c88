/**
 * Examples of using SelectionColumn component
 * 
 * This file demonstrates different ways to add selection functionality
 * to tables using the self-contained SelectionColumn component.
 */

import React, {useState, useMemo} from 'react';
import {TableView} from '@/views';
import {ColumnDef} from '@tanstack/react-table';
import {useSelectionColumn, useSelection} from './SelectionColumn';
import {Button, Stack} from '@mui/material';

// Example data interface
interface ExampleItem {
    id: number;
    name: string;
    email: string;
    status: string;
}

// Example data
const exampleData: ExampleItem[] = [
    {id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'Active'},
    {id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'Inactive'},
    {id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'Active'},
    {id: 4, name: '<PERSON>', email: '<EMAIL>', status: 'Active'},
    {id: 5, name: '<PERSON>', email: 'char<PERSON>@example.com', status: 'Inactive'},
];

/**
 * Example 1: Basic usage with useSelectionColumn hook
 */
export const BasicSelectionExample = () => {
    const [selectedItems, setSelectedItems] = useState<ExampleItem[]>([]);
    const [clearSelection, setClearSelection] = useState(false);

    const handleSelectionChange = (selectedData: ExampleItem[]) => {
        setSelectedItems(selectedData);
    };

    const handleClearSelection = () => {
        setClearSelection(true);
        setSelectedItems([]);
        setTimeout(() => setClearSelection(false), 100);
    };

    const columns: ColumnDef<ExampleItem, unknown>[] = useMemo(() => {
        const selectionColumn = useSelectionColumn({
            getRowId: (row) => row.id,
            data: exampleData,
            onSelectionChange: handleSelectionChange,
            clearSelection,
        });

        return [
            selectionColumn,
            {accessorKey: 'name', header: 'Name'},
            {accessorKey: 'email', header: 'Email'},
            {accessorKey: 'status', header: 'Status'},
        ];
    }, [clearSelection]);

    return (
        <div>
            <div style={{marginBottom: 16}}>
                <h3>Selected Items ({selectedItems.length}):</h3>
                {selectedItems.length > 0 ? (
                    <ul>
                        {selectedItems.map(item => (
                            <li key={item.id}>{item.name} - {item.email}</li>
                        ))}
                    </ul>
                ) : (
                    <p>No items selected</p>
                )}
                <Button onClick={handleClearSelection} disabled={selectedItems.length === 0}>
                    Clear Selection
                </Button>
            </div>
            
            <TableView
                data={exampleData}
                columns={columns}
                tableName="Basic Selection Example"
            />
        </div>
    );
};

/**
 * Example 2: Using useSelection hook independently
 */
export const IndependentSelectionExample = () => {
    const {
        selectedData,
        selectedCount,
        isAllSelected,
        isSomeSelected,
        toggleRowSelection,
        toggleAllRows,
        clearSelection,
        isSelected,
    } = useSelection(exampleData, (row) => row.id);

    const columns: ColumnDef<ExampleItem, unknown>[] = [
        {
            id: 'select',
            header: () => (
                <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={input => {
                        if (input) input.indeterminate = isSomeSelected;
                    }}
                    onChange={toggleAllRows}
                />
            ),
            cell: ({row}) => (
                <input
                    type="checkbox"
                    checked={isSelected(row.original.id)}
                    onChange={() => toggleRowSelection(row.original.id)}
                />
            ),
        },
        {accessorKey: 'name', header: 'Name'},
        {accessorKey: 'email', header: 'Email'},
        {accessorKey: 'status', header: 'Status'},
    ];

    return (
        <div>
            <div style={{marginBottom: 16}}>
                <h3>Selection State:</h3>
                <p>Selected: {selectedCount} items</p>
                <p>All selected: {isAllSelected ? 'Yes' : 'No'}</p>
                <p>Some selected: {isSomeSelected ? 'Yes' : 'No'}</p>
                <Button onClick={clearSelection} disabled={selectedCount === 0}>
                    Clear Selection
                </Button>
            </div>
            
            <TableView
                data={exampleData}
                columns={columns}
                tableName="Independent Selection Example"
            />
        </div>
    );
};

/**
 * Example 3: With bulk actions (like Reviews example)
 */
export const BulkActionsExample = () => {
    const [selectedItems, setSelectedItems] = useState<ExampleItem[]>([]);
    const [clearSelection, setClearSelection] = useState(false);

    const handleSelectionChange = (selectedData: ExampleItem[]) => {
        setSelectedItems(selectedData);
    };

    const handleClearSelection = () => {
        setClearSelection(true);
        setSelectedItems([]);
        setTimeout(() => setClearSelection(false), 100);
    };

    const handleBulkAction = (action: string) => {
        const selectedIds = selectedItems.map(item => item.id);
        console.log(`Bulk ${action} for IDs:`, selectedIds);
        alert(`Would ${action} ${selectedItems.length} items`);
        
        // After action, clear selection
        handleClearSelection();
    };

    const columns: ColumnDef<ExampleItem, unknown>[] = useMemo(() => {
        const selectionColumn = useSelectionColumn({
            getRowId: (row) => row.id,
            data: exampleData,
            onSelectionChange: handleSelectionChange,
            clearSelection,
        });

        return [
            selectionColumn,
            {accessorKey: 'name', header: 'Name'},
            {accessorKey: 'email', header: 'Email'},
            {accessorKey: 'status', header: 'Status'},
        ];
    }, [clearSelection]);

    const hasSelection = selectedItems.length > 0;

    return (
        <TableView
            data={exampleData}
            columns={columns}
            tableName="Bulk Actions Example"
            withTableActions
            tableActions={
                <Stack direction="row" spacing={1}>
                    <Button
                        variant="text"
                        disabled={!hasSelection}
                        onClick={() => handleBulkAction('activate')}
                    >
                        ACTIVATE SELECTED
                    </Button>
                    <Button
                        variant="text"
                        disabled={!hasSelection}
                        onClick={() => handleBulkAction('deactivate')}
                    >
                        DEACTIVATE SELECTED
                    </Button>
                    <Button
                        variant="outlined"
                        disabled={!hasSelection}
                        onClick={handleClearSelection}
                    >
                        CLEAR SELECTION
                    </Button>
                </Stack>
            }
        />
    );
};

/**
 * Example 4: Custom row ID and different data structure
 */
interface CustomItem {
    uuid: string;
    title: string;
    description: string;
    category: string;
}

const customData: CustomItem[] = [
    {uuid: 'abc-123', title: 'Task 1', description: 'First task', category: 'Work'},
    {uuid: 'def-456', title: 'Task 2', description: 'Second task', category: 'Personal'},
    {uuid: 'ghi-789', title: 'Task 3', description: 'Third task', category: 'Work'},
];

export const CustomDataExample = () => {
    const [selectedItems, setSelectedItems] = useState<CustomItem[]>([]);

    const columns: ColumnDef<CustomItem, unknown>[] = useMemo(() => {
        const selectionColumn = useSelectionColumn({
            getRowId: (row) => row.uuid, // Custom ID field
            data: customData,
            onSelectionChange: setSelectedItems,
            checkboxSize: 'medium', // Larger checkboxes
        });

        return [
            selectionColumn,
            {accessorKey: 'title', header: 'Title'},
            {accessorKey: 'description', header: 'Description'},
            {accessorKey: 'category', header: 'Category'},
        ];
    }, []);

    return (
        <div>
            <p>Selected UUIDs: {selectedItems.map(item => item.uuid).join(', ') || 'None'}</p>
            <TableView
                data={customData}
                columns={columns}
                tableName="Custom Data Example"
            />
        </div>
    );
};
