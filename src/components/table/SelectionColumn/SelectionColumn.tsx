import React, {useState, useEffect, useCallback} from 'react';
import {ColumnDef} from '@tanstack/react-table';
import {Checkbox} from '@mui/material';

export interface SelectionColumnProps<T> {
    /**
     * Function to get unique identifier from row data
     */
    getRowId: (row: T) => string | number;
    /**
     * Current table data
     */
    data: T[];
    /**
     * Callback when selection changes
     */
    onSelectionChange?: (selectedData: T[], selectedIds: (string | number)[]) => void;
    /**
     * External trigger to clear selection
     */
    clearSelection?: boolean;
    /**
     * Column ID (default: 'select')
     */
    columnId?: string;
    /**
     * Checkbox size (default: 'small')
     */
    checkboxSize?: 'small' | 'medium';
}

export const useSelectionColumn = <T,>({
    getRowId,
    data,
    onSelectionChange,
    clearSelection,
    columnId = 'select',
    checkboxSize = 'small'
}: SelectionColumnProps<T>): ColumnDef<T, unknown> => {
    const [selectedIds, setSelectedIds] = useState<Set<string | number>>(new Set());

    // Clear selection when external trigger changes
    useEffect(() => {
        if (clearSelection) {
            setSelectedIds(new Set());
        }
    }, [clearSelection]);

    // Clear selection when data changes (new data loaded)
    useEffect(() => {
        setSelectedIds(new Set());
    }, [data]);

    // Notify parent about selection changes
    useEffect(() => {
        if (onSelectionChange) {
            const selectedData = data.filter(row => selectedIds.has(getRowId(row)));
            const selectedIdsArray = Array.from(selectedIds);
            onSelectionChange(selectedData, selectedIdsArray);
        }
    }, [selectedIds, data, onSelectionChange, getRowId]);

    const toggleRowSelection = useCallback((id: string | number) => {
        setSelectedIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }
            return newSet;
        });
    }, []);

    const toggleAllRows = useCallback(() => {
        const allIds = data.map(row => getRowId(row));
        setSelectedIds(prev => {
            const allSelected = allIds.every(id => prev.has(id));
            return allSelected ? new Set() : new Set(allIds);
        });
    }, [data, getRowId]);

    const getHeaderCheckboxState = useCallback(() => {
        if (data.length === 0) return false;
        
        const allIds = data.map(row => getRowId(row));
        const selectedCount = allIds.filter(id => selectedIds.has(id)).length;
        
        if (selectedCount === 0) return false;
        if (selectedCount === allIds.length) return true;
        return 'indeterminate';
    }, [data, selectedIds, getRowId]);

    return {
        id: columnId,
        header: () => {
            const checkboxState = getHeaderCheckboxState();
            return (
                <Checkbox
                    checked={checkboxState === true}
                    indeterminate={checkboxState === 'indeterminate'}
                    onChange={toggleAllRows}
                    size={checkboxSize}
                />
            );
        },
        cell: ({row}) => {
            const rowId = getRowId(row.original);
            return (
                <Checkbox
                    checked={selectedIds.has(rowId)}
                    onChange={() => toggleRowSelection(rowId)}
                    size={checkboxSize}
                />
            );
        },
        enableSorting: false,
        enableHiding: false,
        meta: {
            notExport: true,
        },
    };
};

/**
 * Hook that provides selection state and utilities
 * Can be used independently of the column
 */
export const useSelection = <T,>(
    data: T[],
    getRowId: (row: T) => string | number,
    onSelectionChange?: (selectedData: T[], selectedIds: (string | number)[]) => void
) => {
    const [selectedIds, setSelectedIds] = useState<Set<string | number>>(new Set());

    // Clear selection when data changes
    useEffect(() => {
        setSelectedIds(new Set());
    }, [data]);

    // Notify about changes
    useEffect(() => {
        if (onSelectionChange) {
            const selectedData = data.filter(row => selectedIds.has(getRowId(row)));
            const selectedIdsArray = Array.from(selectedIds);
            onSelectionChange(selectedData, selectedIdsArray);
        }
    }, [selectedIds, data, onSelectionChange, getRowId]);

    const toggleRowSelection = useCallback((id: string | number) => {
        setSelectedIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }
            return newSet;
        });
    }, []);

    const toggleAllRows = useCallback(() => {
        const allIds = data.map(row => getRowId(row));
        setSelectedIds(prev => {
            const allSelected = allIds.every(id => prev.has(id));
            return allSelected ? new Set() : new Set(allIds);
        });
    }, [data, getRowId]);

    const clearSelection = useCallback(() => {
        setSelectedIds(new Set());
    }, []);

    const isSelected = useCallback((id: string | number) => {
        return selectedIds.has(id);
    }, [selectedIds]);

    const selectedData = data.filter(row => selectedIds.has(getRowId(row)));
    const selectedCount = selectedIds.size;
    const isAllSelected = data.length > 0 && data.every(row => selectedIds.has(getRowId(row)));
    const isSomeSelected = selectedIds.size > 0 && !isAllSelected;

    return {
        selectedIds: Array.from(selectedIds),
        selectedData,
        selectedCount,
        isAllSelected,
        isSomeSelected,
        toggleRowSelection,
        toggleAllRows,
        clearSelection,
        isSelected,
    };
};
