import './styles.scss';

import React, {Fragment} from 'react';
import {flexRender, Row, Table} from '@tanstack/react-table';
import {KasSortControls} from '@/components/table';
import {KasNoResults} from '@/components';
import {allTableColumns} from '@/utils/TableUtils';

interface KasTableContentProps<T> {
    table: Table<T>;
    renderExpand?: (row: Row<T>) => React.ReactNode;
    activeRowId?: string;
    onRowClick?: (row: Row<T>) => void;
    getRowBackgroundColor?: (row: Row<T>) => string | undefined;
}

export const KasTableContent = <T,>({
    table,
    renderExpand,
    activeRowId,
    onRowClick,
    getRowBackgroundColor,
}: KasTableContentProps<T>) => {
    return (
        <div className='kas-table-content'>
            <table>
                <thead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <th key={header.id} colSpan={header.colSpan}>
                                        {header.isPlaceholder ? null : (
                                            <div
                                                {...{
                                                    className: header.column.getCanSort()
                                                        ? 'cursor-pointer'
                                                        : '',
                                                    onClick: header.column.getToggleSortingHandler(),
                                                }}>
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext(),
                                                )}
                                                {header.column.columnDef.enableSorting !== false &&
                                                    header.colSpan === 1 && (
                                                        <KasSortControls
                                                            direction={header.column.getIsSorted()}
                                                        />
                                                    )}
                                            </div>
                                        )}
                                    </th>
                                );
                            })}
                        </tr>
                    ))}
                </thead>
                <tbody>
                    {table.getRowModel().rows.map((row, index) => {
                        const customBackgroundColor = getRowBackgroundColor?.(row);

                        // Collect row styles and classes from column meta
                        let metaClassName = '';
                        let metaStyle: React.CSSProperties = {};

                        row.getVisibleCells().forEach((cell) => {
                            const columnMeta = cell.column.columnDef.meta;
                            if (columnMeta?.rowClassName) {
                                const className = columnMeta.rowClassName(row.original);
                                if (className) {
                                    metaClassName += ` ${className}`;
                                }
                            }
                            if (columnMeta?.rowStyle) {
                                const style = columnMeta.rowStyle(row.original);
                                if (style) {
                                    metaStyle = { ...metaStyle, ...style };
                                }
                            }
                        });

                        // Combine all styles
                        const finalStyle = {
                            ...metaStyle,
                            ...(customBackgroundColor && customBackgroundColor.startsWith('#')
                                ? { backgroundColor: customBackgroundColor }
                                : {})
                        };

                        return (
                            <Fragment key={row.id}>
                                <tr
                                    className={`${index % 2 === 0 ? 'odd' : 'even'} ${
                                        row.id === activeRowId ? 'active-row' : ''
                                    } ${customBackgroundColor || ''} ${metaClassName}`.trim()}
                                    style={Object.keys(finalStyle).length > 0 ? finalStyle : undefined}
                                    onClick={() => onRowClick && onRowClick(row)}>
                                {row.getVisibleCells().map((cell) => (
                                    <td key={cell.id} className={cell.column.id}>
                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                    </td>
                                ))}
                            </tr>
                                {row.getIsExpanded() && (
                                    <tr className={index % 2 === 0 ? 'odd' : 'even'}>
                                        <td colSpan={row.getVisibleCells().length}>
                                            {renderExpand && renderExpand(row)}
                                        </td>
                                    </tr>
                                )}
                            </Fragment>
                        );
                    })}
                    {!table.getRowModel().rows.length && (
                        <tr key='no-records' className='odd'>
                            <td colSpan={allTableColumns(table.getAllColumns()).length}>
                                <KasNoResults text='No records found' />
                            </td>
                        </tr>
                    )}
                </tbody>
                {table.getRowModel().rows.length > 0 && (
                    <tfoot>
                        {table.getFooterGroups().map((footerGroup) => {
                            const hasValues = footerGroup.headers
                                .map((header) => {
                                    const isNotGroup = header.id === header.column.id;
                                    const el = flexRender(
                                        header.column.columnDef.footer,
                                        header.getContext(),
                                    );

                                    return isNotGroup && el !== null;
                                })
                                .includes(true);

                            if (!hasValues) {
                                return null;
                            }

                            return (
                                <tr key={footerGroup.id}>
                                    {footerGroup.headers.map((header) => (
                                        <th key={header.id} colSpan={header.colSpan}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef.footer,
                                                      header.getContext(),
                                                  )}
                                        </th>
                                    ))}
                                </tr>
                            );
                        })}
                    </tfoot>
                )}
            </table>
        </div>
    );
};
