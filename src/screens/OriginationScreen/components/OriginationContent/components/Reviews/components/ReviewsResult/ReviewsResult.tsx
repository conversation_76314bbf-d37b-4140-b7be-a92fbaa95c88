import React, {useMemo} from 'react';
import {ReviewsColumns} from './tables';
import {TableView} from '@/views';
import {OriginationReviewModel} from '@/interfaces';
import {useReviews} from '../../useReviews';
import {Button, Stack} from '@mui/material';
import {useOrigination} from '@/screens/OriginationScreen/useOrigination';
import {OriginationItemType, OriginationModalType} from '@/screens/OriginationScreen/interfaces';

export const ReviewsResult = () => {
    const {reviewsState, setSelectedReviews, selectedReviews, loadReviews} = useReviews();
    const {setOpenModal, changeActiveMenu, loadLogs} = useOrigination();

    const items = useMemo(() => {
        return selectedReviews.map((item) => item.gid);
    }, [selectedReviews]);

    const onSuccess = () => {
        changeActiveMenu(OriginationItemType.Logs);
        loadLogs().then();
        setSelectedReviews([]);
        loadReviews().then();
    };

    return (
        <TableView<OriginationReviewModel>
            withTableActions
            columns={ReviewsColumns}
            loading={reviewsState.loading}
            error={reviewsState.error}
            data={reviewsState.data}
            tableName='Reviews Table'
            sortingColumns={[{id: 'last_update_time', desc: true}]}
            onRowSelectionChange={setSelectedReviews}
            tableActions={
                <Stack direction='row' spacing={1}>
                    <Button
                        variant='text'
                        disabled={!items.length}
                        onClick={() =>
                            setOpenModal({
                                type: OriginationModalType.Refresh_Loans,
                                props: {items, onSuccess},
                            })
                        }>
                        REFRESH SELECTED
                    </Button>
                    <Button
                        variant='text'
                        disabled={!items.length}
                        onClick={() =>
                            setOpenModal({
                                type: OriginationModalType.Export_Loans,
                                props: {items, onSuccess},
                            })
                        }>
                        EXPORT SELECTED
                    </Button>
                </Stack>
            }
        />
    );
};
