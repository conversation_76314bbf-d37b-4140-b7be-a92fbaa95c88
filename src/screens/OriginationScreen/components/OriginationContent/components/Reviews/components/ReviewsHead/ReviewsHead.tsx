import React, {useEffect} from 'react';
import {<PERSON>ton, Typography, Grid2, FormControlLabel, Checkbox, Stack} from '@mui/material';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {useReviews} from '../../useReviews';
import {DEFAULT_FILTER_PARAMS} from './../../data';
import {ReviewParamsModel} from './../../interfaces';
import {useOrigination} from '@/screens/OriginationScreen/useOrigination';
import {OriginationItemType} from '@/screens/OriginationScreen/interfaces';

export const ReviewsHead = () => {
    const {activeMenu} = useOrigination();
    const {loadReviews, reviewsState} = useReviews();

    const onSubmit = async (values: ReviewParamsModel) => {
        await loadReviews(values);
    };

    const formik = useFormik<ReviewParamsModel>({
        validateOnMount: true,
        initialValues: DEFAULT_FILTER_PARAMS,
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (activeMenu === OriginationItemType.Review && !reviewsState.data && !reviewsState.loading) {
            formik.handleSubmit();
        }
    }, [activeMenu, reviewsState]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Compliance and Manual Review</Typography>
                </Grid2>
                <Grid2 size={7}>
                    <Stack flexDirection='row' useFlexGap flexWrap='wrap'>
                        <FormControlLabel
                            disabled={reviewsState.loading}
                            label='Show Recent Entries'
                            control={
                                <Checkbox
                                    size='small'
                                    name='recent'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.recent}
                                />
                            }
                        />
                        <FormControlLabel
                            disabled={reviewsState.loading}
                            label='Include Not Exported'
                            control={
                                <Checkbox
                                    size='small'
                                    name='notExported'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.notExported}
                                />
                            }
                        />
                        <FormControlLabel
                            disabled={reviewsState.loading}
                            label='Include Rejected '
                            control={
                                <Checkbox
                                    size='small'
                                    name='rejected'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.rejected}
                                />
                            }
                        />
                    </Stack>
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        disabled={!formik.isValid || reviewsState.loading}>
                        Apply
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
