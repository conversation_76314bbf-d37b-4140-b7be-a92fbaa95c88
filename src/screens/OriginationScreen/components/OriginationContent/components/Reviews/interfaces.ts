export interface ReviewParamsModel {
    recent: boolean;
    notExported: boolean;
    rejected: boolean;
}

export enum ReviewModalType {
    Export_Loans = 'Export Selected Loans',
    Refresh_Loans = 'Refresh Selected Loans',
}

export interface UpdateSelectedLoansProps {
    items: number[];
    onSuccess: () => void;
}

export type ReviewModalProps =
    | {type: ReviewModalType.Export_Loans; props: UpdateSelectedLoansProps}
    | {type: ReviewModalType.Refresh_Loans; props: UpdateSelectedLoansProps};
