import React, {useState} from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useOrigination} from './../../../../../../useOrigination';
import {UpdateSelectedLoansProps} from './../../../../../../interfaces';

interface UpdateLoansProps extends UpdateSelectedLoansProps {
    title: string;
    url: string;
}

export const UpdateLoans = ({title, url, items, onSuccess}: UpdateLoansProps) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal} = useOrigination();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'put',
            body: JSON.stringify({items}),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            onSuccess();
        }
        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='error'>{title}</Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        submitText='OK'
                        loading={submitting}
                        disabled={submitting}
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
