import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {UpdateLoans} from './components';
import {useOrigination} from '@/screens/OriginationScreen/useOrigination';
import {OriginationModalType} from '@/screens/OriginationScreen/interfaces';

export const OriginationModal = () => {
    const {openModal, setOpenModal} = useOrigination();

    const size = useMemo(() => {
        switch (openModal?.type) {
            case OriginationModalType.Export_Loans:
            case OriginationModalType.Refresh_Loans:
                return 'small';
            default:
                return 'medium';
        }
    }, [openModal?.type]);

    const renderForm = useMemo(() => {
        switch (openModal?.type) {
            case OriginationModalType.Export_Loans:
                return (
                    <UpdateLoans
                        title='Re-export all selected loans?'
                        url='/api/secured/origination/reviews/fulfill'
                        {...openModal.props}
                    />
                );
            case OriginationModalType.Refresh_Loans:
                return (
                    <UpdateLoans
                        title='Refresh all selected loans?'
                        url='/api/secured/origination/reviews/refresh'
                        {...openModal.props}
                    />
                );
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Action Modal'}
            size={size}
            open={!!openModal}
            onClose={() => setOpenModal(null)}>
            {renderForm}
        </KasModal>
    );
};
