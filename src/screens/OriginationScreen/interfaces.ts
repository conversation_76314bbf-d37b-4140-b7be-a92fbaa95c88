export enum OriginationItemType {
    Logs = 'LOGS',
    Review = 'REVIEW',
}

export interface LogsFilterParamsModel {
    length?: string;
}

export enum OriginationModalType {
    Export_Loans = 'Export Selected Loans',
    Refresh_Loans = 'Refresh Selected Loans',
}

export interface UpdateSelectedLoansProps {
    items: number[];
    onSuccess: () => void;
}

export type OriginationModalProps =
    | {type: OriginationModalType.Export_Loans; props: UpdateSelectedLoansProps}
    | {type: OriginationModalType.Refresh_Loans; props: UpdateSelectedLoansProps};
