'use client';
import './styles.scss';

import React from 'react';
import {
    KasDefaultTable,
    KasLoading,
    KasL<PERSON><PERSON>BackD<PERSON>,
    KasLoadingError,
    KasPureTable,
    KasSwitch,
    KasSwitch<PERSON>hen,
} from '@/components';
import {KasTableProps} from '@/components/KasTable';
import Box from '@mui/material/Box';

interface TableViewPropsBase<T> extends KasTableProps<T> {
    loading?: boolean;
    error?: string;
    hideBackDropLoading?: boolean;
    onRetry?: () => void;
}

type TableViewProps<T> =
    | (TableViewPropsBase<T> & {withTableActions?: false})
    | (TableViewPropsBase<T> & {
          withTableActions: true;
          tableActions?: React.ReactNode;
      });

export const TableView = <T,>(props: TableViewProps<T>) => {
    const {loading = false, error = '', data, columns, hideBackDropLoading = false, onRetry, ...rest} = props;
    const withTableActions = !!props.withTableActions;

    return (
        <div className='kas-table-view'>
            <KasSwitch>
                <KasSwitchWhen condition={!data && loading}>
                    <Box p={withTableActions ? 9 : 5.5}>
                        <KasLoading size={32} />
                    </Box>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!error}>
                    <KasLoadingError view='contained' error={error} onTryAgain={onRetry} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!data}>
                    {loading && !hideBackDropLoading && <KasLoadingBackDrop />}
                    {withTableActions ? (
                        <KasDefaultTable<T> columns={columns} data={data as T[]} {...rest}>
                            {props.tableActions}
                        </KasDefaultTable>
                    ) : (
                        <KasPureTable<T> columns={columns} data={data as T[]} {...rest} />
                    )}
                </KasSwitchWhen>
            </KasSwitch>
        </div>
    );
};
